# ESP32 Color Matcher

A sophisticated ESP32-based color measurement device featuring a TCS3430 sensor with advanced colorimetric calibration and a modern React TypeScript web interface.

## 🌈 Features

- **High-Precision Color Measurement**: TCS3430 sensor with matrix-based colorimetric calibration
- **Advanced Calibration System**:
  - Matrix-based 3x4 transformation calibration using 7-color patches
  - White point calibration with Dulux Vivid White reference
  - Black point calibration for true darkness measurement
  - CIE 1931 color space conversion with chromatic adaptation
- **Enhanced LED Control**:
  - Automatic brightness optimization
  - Real-time sensor feedback with color-coded status indicators
  - Manual and enhanced auto-adjustment modes
- **Modern Web Interface**: React TypeScript application with real-time updates
- **Google Apps Script Integration**: Cloud-based color matching with Dulux paint database
- **Automated Deployment**: PowerShell scripts for streamlined firmware and web interface deployment

## 🛠 Hardware Requirements

- **ESP32-S3 DevKit-C-1** (or compatible ESP32 board)
- **TCS3430 Color Sensor** (DFRobot or compatible)
- **LED** for illumination (connected to pin 5)
- **I2C Connection**: SDA/SCL pins for sensor communication

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **PlatformIO** (for ESP32 firmware development)
- **Python** (for test scripts)
- **PowerShell** (for deployment scripts on Windows)

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd esp32-color-matcher
npm install
```

### 2. Configure Environment
Create a `.env` file with your device IP:
```
DEVICE_IP=*************
```

### 3. Build and Deploy Web Interface
```bash
npm run build
npm run deploy
```

### 4. Flash ESP32 Firmware
```bash
pio run --target upload
```

### 5. Access the Interface
Open your browser to `http://*************` (or your device IP)

## 🔧 Development

### Web Interface Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run deploy       # Deploy to ESP32
```

### ESP32 Firmware Development
```bash
pio run              # Build firmware
pio run --target upload    # Upload to device
pio device monitor   # Monitor serial output
```

### Testing
```bash
# Run various test scripts
./quick_test.ps1
python test_calibration.py
python mock_esp32_server.py  # For offline testing
```
